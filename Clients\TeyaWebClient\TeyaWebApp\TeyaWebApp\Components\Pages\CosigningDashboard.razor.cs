using Microsoft.AspNetCore.Components;
using MudBlazor;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;

namespace TeyaWebApp.Components.Pages
{
    /// <summary>
    /// Professional cosigning dashboard for managing review requests
    /// </summary>
    public partial class CosigningDashboard : ComponentBase
    {
        #region Injected Services
        [Inject] private ActiveUser CurrentUser { get; set; }
        [Inject] private UserContext UserContext { get; set; }
        [Inject] private ICosigningRequestService CosigningRequestService { get; set; }
        [Inject] private ILogger<CosigningDashboard> Logger { get; set; }
        [Inject] private ISnackbar Snackbar { get; set; }
        [Inject] private IStringLocalizer<CosigningDashboard> Localizer { get; set; }
        [Inject] private NavigationManager Navigation { get; set; }
        #endregion

        #region Private Fields
        private List<CosigningRequest> AllRequests = new();
        private List<CosigningRequest> PendingRequests = new();
        private List<CosigningRequest> RecentRequests = new();
        private HashSet<Guid> SelectedRequests = new();
        
        private bool IsLoading = false;
        private bool ShowReviewDialog = false;
        private Guid SelectedRequestForReview = Guid.Empty;
        
        // Filters
        private CosigningRequestStatus? SelectedStatus = null;
        private DateTime? SelectedDate = null;
        private string SearchText = string.Empty;
        private int DisplayLimit = 10;
        
        // Statistics
        private int PendingCount = 0;
        private int ApprovedCount = 0;
        private int ChangesRequestedCount = 0;
        private int TotalRequestsCount = 0;
        #endregion

        #region Computed Properties
        private IEnumerable<CosigningRequest> FilteredPendingRequests
        {
            get
            {
                var filtered = PendingRequests.AsEnumerable();

                if (SelectedStatus.HasValue)
                {
                    filtered = filtered.Where(r => r.Status == SelectedStatus.Value);
                }

                if (SelectedDate.HasValue)
                {
                    filtered = filtered.Where(r => r.RequestDate.Date == SelectedDate.Value.Date);
                }

                if (!string.IsNullOrWhiteSpace(SearchText))
                {
                    filtered = filtered.Where(r => 
                        r.PatientName.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                        r.RequesterName.Contains(SearchText, StringComparison.OrdinalIgnoreCase));
                }

                return filtered.OrderByDescending(r => r.RequestDate);
            }
        }
        #endregion

        #region Lifecycle Methods
        protected override async Task OnInitializedAsync()
        {
            await LoadData();
        }
        #endregion

        #region Data Loading Methods
        private async Task LoadData()
        {
            try
            {
                IsLoading = true;
                StateHasChanged();

                var reviewerId = Guid.Parse(CurrentUser.id);

                // Load pending requests for the current reviewer
                PendingRequests = (await CosigningRequestService.GetByReviewerIdAsync(
                    reviewerId,
                    UserContext.ActiveUserOrganizationID,
                    UserContext.ActiveUserSubscription))?.ToList() ?? new List<CosigningRequest>();

                // Load recent requests (last 7 days)
                var recentDate = DateTime.UtcNow.AddDays(-7);
                RecentRequests = PendingRequests
                    .Where(r => r.RequestDate >= recentDate)
                    .OrderByDescending(r => r.RequestDate)
                    .ToList();

                // Calculate statistics
                CalculateStatistics();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading cosigning dashboard data");
                Snackbar.Add(Localizer["ErrorLoadingData"], Severity.Error);
            }
            finally
            {
                IsLoading = false;
                StateHasChanged();
            }
        }

        private void CalculateStatistics()
        {
            var today = DateTime.UtcNow.Date;
            
            PendingCount = PendingRequests.Count(r => r.Status == CosigningRequestStatus.Pending);
            ApprovedCount = PendingRequests.Count(r => r.Status == CosigningRequestStatus.Approved && r.RequestDate.Date == today);
            ChangesRequestedCount = PendingRequests.Count(r => r.Status == CosigningRequestStatus.ChangesRequested);
            TotalRequestsCount = PendingRequests.Count;
        }
        #endregion

        #region Action Methods
        private async Task RefreshData()
        {
            await LoadData();
            Snackbar.Add(Localizer["DataRefreshed"], Severity.Success);
        }

        private void OpenReview(Guid requestId)
        {
            SelectedRequestForReview = requestId;
            ShowReviewDialog = true;
        }

        private void CloseReviewDialog()
        {
            ShowReviewDialog = false;
            SelectedRequestForReview = Guid.Empty;
        }

        private async Task OnReviewCompleted()
        {
            CloseReviewDialog();
            await LoadData(); // Refresh data after review completion
        }

        private async Task QuickApprove(Guid requestId)
        {
            try
            {
                var reviewerId = Guid.Parse(CurrentUser.id);
                var reviewerName = CurrentUser.displayName ?? CurrentUser.givenName ?? "Unknown Reviewer";

                var request = PendingRequests.FirstOrDefault(r => r.Id == requestId);
                await CosigningRequestService.ApproveRequestAsync(
                    requestId,
                    reviewerName,
                    request?.PatientName ?? "Unknown Patient",
                    request?.PatientGender ?? "Unknown",
                    "Approved");

                Snackbar.Add(Localizer["RequestApprovedSuccessfully"], Severity.Success);
                await LoadData();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error quick approving request {RequestId}", requestId);
                Snackbar.Add(Localizer["ErrorApprovingRequest"], Severity.Error);
            }
        }

        private void RequestChanges(Guid requestId)
        {
            // Open the review dialog for detailed commenting
            OpenReview(requestId);
        }

        private async Task BulkAction(BulkActionType actionType)
        {
            if (!SelectedRequests.Any()) return;

            try
            {
                var reviewerId = Guid.Parse(CurrentUser.id);
                var reviewerName = CurrentUser.displayName ?? CurrentUser.givenName ?? "Unknown Reviewer";

                foreach (var requestId in SelectedRequests)
                {
                    if (actionType == BulkActionType.Approve)
                    {
                        var request = PendingRequests.FirstOrDefault(r => r.Id == requestId);
                        await CosigningRequestService.ApproveRequestAsync(
                            requestId,
                            reviewerName,
                            request?.PatientName ?? "Unknown Patient",
                            request?.PatientGender ?? "Unknown",
                            "Approved");
                    }
                }

                var message = actionType == BulkActionType.Approve 
                    ? Localizer["BulkApprovalCompleted"] 
                    : Localizer["BulkActionCompleted"];
                
                Snackbar.Add(message, Severity.Success);
                SelectedRequests.Clear();
                await LoadData();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error performing bulk action {ActionType}", actionType);
                Snackbar.Add(Localizer["ErrorPerformingBulkAction"], Severity.Error);
            }
        }

        private void ApplyFilters()
        {
            StateHasChanged();
        }

        private void ClearFilters()
        {
            SelectedStatus = null;
            SelectedDate = null;
            SearchText = string.Empty;
            StateHasChanged();
        }

        private void LoadMore()
        {
            DisplayLimit += 10;
            StateHasChanged();
        }
        #endregion

        #region Selection Methods
        private bool GetSelectionState(Guid requestId)
        {
            return SelectedRequests.Contains(requestId);
        }

        private void ToggleSelection(Guid requestId, bool isSelected)
        {
            if (isSelected)
            {
                SelectedRequests.Add(requestId);
            }
            else
            {
                SelectedRequests.Remove(requestId);
            }
            StateHasChanged();
        }
        #endregion

        #region Helper Methods
        private string GetPriorityClass(CosigningRequest request)
        {
            var daysSinceRequest = (DateTime.UtcNow - request.RequestDate).Days;
            
            if (daysSinceRequest > 2) return "priority-high";
            if (daysSinceRequest > 1) return "priority-normal";
            return "priority-low";
        }

        private string GetStatusClass(CosigningRequestStatus status)
        {
            return status switch
            {
                CosigningRequestStatus.Pending => "status-pending",
                CosigningRequestStatus.Approved => "status-approved",
                CosigningRequestStatus.ChangesRequested => "status-changes-requested",
                _ => "status-pending"
            };
        }

        private string GetStatusText(CosigningRequestStatus status)
        {
            return status switch
            {
                CosigningRequestStatus.Pending => Localizer["Pending"],
                CosigningRequestStatus.Approved => Localizer["Approved"],
                CosigningRequestStatus.ChangesRequested => Localizer["ChangesRequested"],
                _ => Localizer["Unknown"]
            };
        }

        private int GetCommentCount(CosigningRequest request)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(request.CommentsJson))
                    return 0;

                var comments = System.Text.Json.JsonSerializer.Deserialize<List<CosigningComment>>(request.CommentsJson);
                return comments?.Count ?? 0;
            }
            catch
            {
                return 0;
            }
        }

        private string GetActivityText(CosigningRequest request)
        {
            var timeAgo = GetTimeAgo(request.RequestDate);
            
            return request.Status switch
            {
                CosigningRequestStatus.Approved => $"{Localizer["ApprovedBy"]} {request.ReviewerName} {timeAgo}",
                CosigningRequestStatus.ChangesRequested => $"{Localizer["ChangesRequestedBy"]} {request.ReviewerName} {timeAgo}",
                _ => $"{Localizer["RequestedBy"]} {request.RequesterName} {timeAgo}"
            };
        }

        private string GetTimeAgo(DateTime dateTime)
        {
            var timeSpan = DateTime.UtcNow - dateTime;
            
            if (timeSpan.Days > 0)
                return $"{timeSpan.Days} {(timeSpan.Days == 1 ? Localizer["DayAgo"] : Localizer["DaysAgo"])}";
            
            if (timeSpan.Hours > 0)
                return $"{timeSpan.Hours} {(timeSpan.Hours == 1 ? Localizer["HourAgo"] : Localizer["HoursAgo"])}";
            
            if (timeSpan.Minutes > 0)
                return $"{timeSpan.Minutes} {(timeSpan.Minutes == 1 ? Localizer["MinuteAgo"] : Localizer["MinutesAgo"])}";
            
            return Localizer["JustNow"];
        }
        #endregion
    }
}
